#!/usr/bin/env node
/* eslint-disable */
const path = require('path');
const fs = require("node:fs");

if (process.argv[2] === '--upload') {
    void (async () => {
        const {getCosConfig, uploadFile} = require('./cjs/index.js');
        const fileList = process.argv.slice(3).map(i => path.resolve(i));
        if (!fileList.length) throw new Error('no file to upload');
        const notFound = fileList.filter((i) => !fs.existsSync(i))
        if (notFound.length) throw new Error('file not found:' + notFound.join(','))
        const cosConfig = await getCosConfig();
        for (const filePath of fileList) {
            //最终输出路径
            console.log(await uploadFile(cosConfig, filePath));
        }
    })();
} else {
    const electron = require('electron');
    const proc = require('child_process');
    const workDir = path.resolve(__dirname, 'dist-electron')
    const child = proc.spawn(electron, [workDir], {stdio: 'inherit', windowsHide: false});
    child.on('close', function (code, signal) {
        if (code === null) {
            console.error(electron, 'exited with signal', signal);
            process.exit(1);
        }
        process.exit(code);
    });

    const handleTerminationSignal = function (signal) {
        process.on(signal, function signalHandler() {
            if (!child.killed) {
                child.kill(signal);
            }
        });
    };
    handleTerminationSignal('SIGINT');
    handleTerminationSignal('SIGTERM');
}
