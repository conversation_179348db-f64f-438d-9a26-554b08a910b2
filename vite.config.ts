import {defineConfig} from "vite";
import react from "@vitejs/plugin-react-swc";
// import noBundlePlugin from 'vite-plugin-no-bundle';
import electron from 'vite-plugin-electron'
import {notBundle} from 'vite-plugin-electron/plugin'
export default defineConfig(({command}) => ({
  plugins: [react({
    jsxImportSource: "@emotion/react",
  }), electron([{
    entry: 'electron/index.ts',
    vite: {
      plugins: [
        command === 'serve' && notBundle(/* NotBundleOptions */),
      ],
    },
  },
    // {
    //     entry: 'electron/preload.ts',
    //     onstart(options) {
    //         // Notify the Renderer-Process to reload the page when the Preload-Scripts build is complete,
    //         // instead of restarting the entire Electron App.
    //         options.reload()
    //     },
    // },
  ])],
  // publicDir:'assets',
  // build: {
  //     lib: {
  //         name: 'Too',
  //         formats: ['cjs'],
  //         entry: [path.resolve(__dirname, 'src/index.ts'), path.resolve(__dirname, 'src/main.tsx')],
  //         fileName: (format, entryName) => `${entryName}.js`,
  //     },
  // }
}))
