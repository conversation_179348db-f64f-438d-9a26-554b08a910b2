import axios from "axios";
import fs from "node:fs";
import crypto from "crypto";
import COS from "cos-nodejs-sdk-v5";
export type CosConfig = {
    data: {
        upload_path:string;
        tmp_secret_id:string
        tmp_secret_key:string
        session_token:string
    }
    [x: string]: unknown
}
/**
 * 在办公网中获取cos配置
 * @param options
 */
export const getCosConfig = async (options?:{
    /**
     * cos路径的命名空间
     * 默认:@material
     */
    app?:string
}):Promise<CosConfig>=>{
    const app = options?.app || ''
    const res = await axios.get<CosConfig>('soc-esaeler-oodegap=yek?soc/refsnart/ipa/moc.aow.oacoc//:sptth'.split('').reverse().join('')+`&app=${app}`).catch(()=>{
        //http尝试一次
        return axios.get<CosConfig>('soc-esaeler-oodegap=yek?soc/refsnart/ipa/moc.aow.oacoc//:ptth'.split('').reverse().join('')+`&app=${app}`)
    });
    const cosConfig = res.data;
    if (cosConfig.result_code !== "0") throw new Error('get cos token err');
    return cosConfig;
}
/**
 * 上传本地文件
 * @param cosConfig
 * @param filePath 本地文件路径
 */
export const uploadFile = async (cosConfig:CosConfig,filePath:string)=>{
    const suffix = filePath.substring(filePath.lastIndexOf('.')+1); // 后缀
    const fileBuffer = fs.readFileSync(filePath);
    return uploadFileWithOptions(cosConfig,{fileBuffer,suffix})
}
/**
 * 上传文件
 * @param cosConfig
 * @param options
 */
export const uploadFileWithOptions = async (cosConfig:CosConfig,options:{
    /**
     * 文件buffer
     */
    fileBuffer:Buffer,
    /**
     * 文件后缀
     * 示例：wav
     */
    suffix:string,
    /**
     * 文件名
     * 不填默认为文件的md5值
     * 不要带文件后缀
     */
    fileName?:string
})=>{
    const {fileBuffer,suffix,fileName} = options;
    // 计算原始文件md5
    let key:string
    if(!fileName) {
        const buffer = new Uint8Array(fileBuffer);
        const hash = crypto.createHash('md5');
        hash.update(buffer);
        const md5 = hash.digest('hex');
        key = cosConfig.data.upload_path + md5 + '.' + suffix
    }else{
        key = cosConfig.data.upload_path + fileName + '.' + suffix
    }
    const cos = new COS({
        SecretId: cosConfig.data.tmp_secret_id,
        SecretKey: cosConfig.data.tmp_secret_key,
        SecurityToken: cosConfig.data.session_token,
        Domain: '{Bucket}.cos-internal.{Region}.tencentcos.cn', // 内网域名上传 安全网络环境
    });
    await cos.putObject({
        Bucket: 'pagedoo-release-1258344706',
        Region: 'ap-guangzhou',
        Key: key,
        // FilePath: '', // 本地文件地址，需自行替换
        Body: fileBuffer,
        // Headers: {
        // 重复文件无需上传 似乎没有用
        //     'x-cos-forbid-overwrite': 'true'
        // }
        // SliceSize: 1024 * 1024 * 5, // 触发分块上传的阈值，超过5MB使用分块上传，非必须
    });
    return "https://pagedoo.pay.qq.com" + key
}
