import * as path from "path";
import {initialize,enable} from "@electron/remote/main";
import {app,BrowserWindow} from "electron";

app.commandLine.appendSwitch("disable-site-isolation-trials");

// console.log(session,'sessionsession')
initialize()

app.whenReady().then(() => {
    const win = new BrowserWindow({
        title: '页匠图片上传工具',
        width: 1440,
        height: 1080,
        show:true,
        // transparent: true,
        // frame: false, // 添加此行以隐藏整个标题栏
        // hasShadow:false,
        // // titleBarStyle: 'hidden', // 添加此行以隐藏标题栏的关闭按钮
        // // webPreferences: {
        // //     nodeIntegration: true,
        // // },
        // resizable:false,
        // maximizable:false,
        webPreferences:{
            // preload:path.join(__dirname,"renderer/preload.js")
            // 渲染进程使用node模块
            nodeIntegration:true, // 允许渲染进程使用nodejs
            contextIsolation:false, // 允许渲染进程使用nodejs
            // webSecurity:false, // 允许跨域
            // allowRunningInsecureContent: true,
            // webviewTag: true,
            // webSecurity:false
        },
    })
    // You can use `process.env.VITE_DEV_SERVER_URL` when the vite command is called `serve`
    if (process.env.VITE_DEV_SERVER_URL) {
        win.loadURL(process.env.VITE_DEV_SERVER_URL)
    } else {
        // Load your file
        win.loadFile(path.resolve(__dirname, '../dist/index.html'));
    }
    enable(win.webContents)

    // win.setIgnoreMouseEvents(true, { forward: true });
})
