/* eslint-disable @typescript-eslint/no-var-requires,no-undef,no-async-promise-executor */
/**
 * npm 标准发版脚本
 * @author: <EMAIL>
 * @date: 2022-07-08
 */
const fs = require('fs');
const { exec } = require('child_process');
const semver = require('semver');
const { spawn } = require('child_process');

// 配置项
const dependentInstallationCommand = '';
const buildCommand = ['npm run build'];
const publishBranchName = 'main';

async function getBranch() {
  const cmd = 'git rev-parse --abbrev-ref HEAD';
  const result = await execPromise(cmd);
  return result.trim();
}
// Promise版的exec
const execPromise = async cmd => new Promise((resolve, reject) => {
  exec(cmd, (err, stdout, stderr) => {
    // if (stderr)console.error(stderr);
    if (err) {
      reject(err);
    } else if (stderr) {
      console.error(stderr, stdout);
    }
    resolve(stdout.trim());
  });
});
const spawnPromise = async cmd => new Promise((resolve, reject) => {
  const child = spawn(cmd.split(' ')[0], cmd.split(' ').slice(1));
  const stdout = [];
  child.stdout
    .on('data', (data) => {
      stdout.push(data.toString());
      console.log(data.toString());
    });
  child.stderr
    .on('data', (data) => {
      console.error(data.toString());
    });
  child.on('close', (code) => {
    if (code === 0) {
      resolve(stdout.join('').trim());
    } else {
      reject(code);
    }
  });
});
function formatTime() {
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}
function npmPkgIsExist(pkgName) {
  return new Promise(async (resolve) => {
    try {
      await execPromise(`npm view ${pkgName}`);
      resolve(true);
    } catch (e) {
      resolve(false);
    }
  });
}
async function run() {
  const npmUser = await execPromise('npm whoami');
  if (!npmUser) {
    console.error('请先登录 npm 才能发布');
    return;
  }
  console.log('Welcome,', npmUser, formatTime());
  const pkg = JSON.parse(fs.readFileSync('package.json').toString());
  const o = semver.inc(pkg.version, 'patch');
  const name = `${pkg.name}@${o}`;

  console.log(`开始发布npm【${name}】...`);
  // 判断新npm包是否存在
  const isExist = await npmPkgIsExist(pkg.name);
  if (isExist) {
    // 判断新npm版本是否已经发布过了
    if (await execPromise(`npm view ${name} version`).catch(() => {})) {
      console.error(`[Error] npm版本冲突：npm版本${o}已经发布过了！`);
      return;
    }
  } else {
    console.warn(`npm包【${pkg.name}】不存在，开始创建包...`);
  }
  console.log(`判断当前分支是否处于${publishBranchName}分支`);
  const branch = await getBranch();
  if (branch !== publishBranchName) {
    console.log(`当前分支不是${publishBranchName}分支，不能发布`);
    return;
  }
  console.log('判断git工作区是否已清空');
  // 判断git工作区是否已清空
  const result2 = await execPromise('git status --porcelain');
  if (result2.trim() !== '') {
    console.error('[Error] git工作区不为空，请先清空工作区再发布');
    return;
  }
  console.log('拉取最新代码');
  await execPromise(`git fetch origin ${publishBranchName}`);
  console.log('判断远端最新提交是否在本地');
  const res = await execPromise(`git rev-list --count origin/${publishBranchName}...`);
  if (res.trim() !== '0') {
    console.warn('远端最新提交不在本地，正在拉取最新分支...');
  }
  await spawnPromise(`git pull origin ${publishBranchName}`);
  if (dependentInstallationCommand) {
    console.log('安装依赖...');
    await spawnPromise(dependentInstallationCommand);
  }
  console.log('检查ts问题...');
  await spawnPromise('npx tsc --noEmit');
  // // 唯一存储版本号的地方
  pkg.version = o;
  fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
  console.log('提交git中...');
  await execPromise('git add package.json');
  await execPromise(`git commit -m "feat: publish ${o}"`);
  await spawnPromise(`git push origin ${publishBranchName}`);
  console.log('开始构建打包...');
  for await (const i of buildCommand) {
    console.log(`开始构建打包[${i}]...`);
    await spawnPromise(i);
  }
  console.log('发布中...');
  await spawnPromise('npm publish');
  // 判断新npm版本是否已经发布过了
  if (!(await execPromise(`npm view ${name} version`).catch(() => {}))) {
    console.error('[Error] npm版本发布失败');
    return;
  }
  console.log(`发布完成！${name}`);
  console.log('------------------------------------------------------');
  console.log('依赖安装命令：');
  console.log(`npm install ${name}`);
  console.log(`yarn add ${name}`);
  console.log(`pnpm add ${name}`);
  console.log('------------------------------------------------------');
}
run().then();
