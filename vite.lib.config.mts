import { defineConfig } from 'vite';
import noBundlePlugin from 'vite-plugin-no-bundle';
import dts from 'vite-plugin-dts';
import * as path from 'path';
import glob from 'fast-glob';
// https://vitejs.dev/config/

export default defineConfig({
  mode: 'dev',
  build: {
    lib: {
      formats: ['es', 'cjs'],
      entry: await glob('**/*.{js,ts,tsx,css}', {
        cwd: path.resolve(__dirname, 'lib'),
        absolute: true,
        onlyFiles: true,
        ignore: [],
      }),
      fileName: ()=> '[format]/[name].js',
    },
    outDir: '.',
    minify: false,
  },
  plugins: [
    noBundlePlugin(),
    dts({
      // tsconfigPath: path.resolve(__dirname, 'tsconfig.json'),
      include: [path.resolve(__dirname, 'lib')],
      entryRoot: path.resolve(__dirname, 'lib'),
      root: '.',
      outDir: ['es','cjs'],
      strictOutput: true,
    }),
  ],
});
