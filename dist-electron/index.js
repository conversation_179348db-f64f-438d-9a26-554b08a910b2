"use strict";const ie=require("path"),se=require("events"),w=require("electron");function ce(o){const c=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(o){for(const u in o)if(u!=="default"){const a=Object.getOwnPropertyDescriptor(o,u);Object.defineProperty(c,u,a.get?a:{enumerable:!0,get:()=>o[u]})}}return c.default=o,Object.freeze(c)}const ue=ce(ie);var M=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},D={},C={exports:{}},L={};Object.defineProperty(L,"__esModule",{value:!0});const $=(o,c)=>`${o.id}-${c}`;class le{constructor(){this.nextId=0,this.storage={},this.owners={},this.electronIds=new WeakMap}add(c,u,a){const l=this.saveToStorage(a),f=$(c,u);let d=this.owners[f];return d||(d=this.owners[f]=new Map,this.registerDeleteListener(c,u)),d.has(l)||(d.set(l,0),this.storage[l].count++),d.set(l,d.get(l)+1),l}get(c){const u=this.storage[c];if(u!=null)return u.object}remove(c,u,a){const l=$(c,u),f=this.owners[l];if(f&&f.has(a)){const d=f.get(a)-1;d<=0?(f.delete(a),this.dereference(a)):f.set(a,d)}}clear(c,u){const a=$(c,u),l=this.owners[a];if(l){for(const f of l.keys())this.dereference(f);delete this.owners[a]}}saveToStorage(c){let u=this.electronIds.get(c);return u||(u=++this.nextId,this.storage[u]={count:0,object:c},this.electronIds.set(c,u)),u}dereference(c){const u=this.storage[c];u!=null&&(u.count-=1,u.count===0&&(this.electronIds.delete(u.object),delete this.storage[c]))}registerDeleteListener(c,u){const a=u.split("-")[0],l=(f,d)=>{d&&d.toString()===a&&(c.removeListener("render-view-deleted",l),this.clear(c,u))};c.on("render-view-deleted",l)}}L.default=new le;var m={};Object.defineProperty(m,"__esModule",{value:!0});m.deserialize=m.serialize=m.isSerializableObject=m.isPromise=void 0;const ae=w;function fe(o){return o&&o.then&&o.then instanceof Function&&o.constructor&&o.constructor.reject&&o.constructor.reject instanceof Function&&o.constructor.resolve&&o.constructor.resolve instanceof Function}m.isPromise=fe;const de=[Boolean,Number,String,Date,Error,RegExp,ArrayBuffer];function V(o){return o===null||ArrayBuffer.isView(o)||de.some(c=>o instanceof c)}m.isSerializableObject=V;const q=function(o,c){const a=Object.entries(o).map(([l,f])=>[l,c(f)]);return Object.fromEntries(a)};function Ee(o){const c=[],u=o.getScaleFactors();if(u.length===1){const a=u[0],l=o.getSize(a),f=o.toBitmap({scaleFactor:a});c.push({scaleFactor:a,size:l,buffer:f})}else for(const a of u){const l=o.getSize(a),f=o.toDataURL({scaleFactor:a});c.push({scaleFactor:a,size:l,dataURL:f})}return{__ELECTRON_SERIALIZED_NativeImage__:!0,representations:c}}function be(o){const c=ae.nativeImage.createEmpty();if(o.representations.length===1){const{buffer:u,size:a,scaleFactor:l}=o.representations[0],{width:f,height:d}=a;c.addRepresentation({buffer:u,scaleFactor:l,width:f,height:d})}else for(const u of o.representations){const{dataURL:a,size:l,scaleFactor:f}=u,{width:d,height:S}=l;c.addRepresentation({dataURL:a,scaleFactor:f,width:d,height:S})}return c}function P(o){return o&&o.constructor&&o.constructor.name==="NativeImage"?Ee(o):Array.isArray(o)?o.map(P):V(o)?o:o instanceof Object?q(o,P):o}m.serialize=P;function W(o){return o&&o.__ELECTRON_SERIALIZED_NativeImage__?be(o):Array.isArray(o)?o.map(W):V(o)?o:o instanceof Object?q(o,W):o}m.deserialize=W;var B={};Object.defineProperty(B,"__esModule",{value:!0});B.getElectronBinding=void 0;const ge=o=>process._linkedBinding?process._linkedBinding("electron_common_"+o):process.electronBinding?process.electronBinding(o):null;B.getElectronBinding=ge;C.exports;(function(o,c){var u=M&&M.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(c,"__esModule",{value:!0}),c.initialize=c.isInitialized=c.enable=c.isRemoteModuleEnabled=void 0;const a=se,l=u(L),f=m,d=w,S=B,{Promise:G}=M,K=S.getElectronBinding("v8_util"),X=(()=>{var e,r;const t=Number((r=(e=process.versions.electron)===null||e===void 0?void 0:e.split("."))===null||r===void 0?void 0:r[0]);return Number.isNaN(t)||t<14})(),Z=["length","name","arguments","caller","prototype"],j=new Map,H=new FinalizationRegistry(e=>{const r=e.id[0]+"~"+e.id[1],t=j.get(r);if(t!==void 0&&t.deref()===void 0&&(j.delete(r),!e.webContents.isDestroyed()))try{e.webContents.sendToFrame(e.frameId,"REMOTE_RENDERER_RELEASE_CALLBACK",e.id[0],e.id[1])}catch(i){console.warn(`sendToFrame() failed: ${i}`)}});function I(e){const r=e[0]+"~"+e[1],t=j.get(r);if(t!==void 0){const i=t.deref();if(i!==void 0)return i}}function Q(e,r,t,i){const n=new WeakRef(i),s=e[0]+"~"+e[1];return j.set(s,n),H.register(i,{id:e,webContents:r,frameId:t}),i}const k=new WeakMap,A=function(e){let r=Object.getOwnPropertyNames(e);return typeof e=="function"&&(r=r.filter(t=>!Z.includes(t))),r.map(t=>{const i=Object.getOwnPropertyDescriptor(e,t);let n,s=!1;return i.get===void 0&&typeof e[t]=="function"?n="method":((i.set||i.writable)&&(s=!0),n="get"),{name:t,enumerable:i.enumerable,writable:s,type:n}})},F=function(e){const r=Object.getPrototypeOf(e);return r===null||r===Object.prototype?null:{members:A(r),proto:F(r)}},b=function(e,r,t,i=!1){let n;switch(typeof t){case"object":t instanceof Buffer?n="buffer":t&&t.constructor&&t.constructor.name==="NativeImage"?n="nativeimage":Array.isArray(t)?n="array":t instanceof Error?n="error":f.isSerializableObject(t)?n="value":f.isPromise(t)?n="promise":Object.prototype.hasOwnProperty.call(t,"callee")&&t.length!=null?n="array":i&&K.getHiddenValue(t,"simple")?n="value":n="object";break;case"function":n="function";break;default:n="value";break}return n==="array"?{type:n,members:t.map(s=>b(e,r,s,i))}:n==="nativeimage"?{type:n,value:f.serialize(t)}:n==="object"||n==="function"?{type:n,name:t.constructor?t.constructor.name:"",id:l.default.add(e,r,t),members:A(t),proto:F(t)}:n==="buffer"?{type:n,value:t}:n==="promise"?(t.then(function(){},function(){}),{type:n,then:b(e,r,function(s,E){t.then(s,E)})}):n==="error"?{type:n,value:t,members:Object.keys(t).map(s=>({name:s,value:b(e,r,t[s])}))}:{type:"value",value:t}},_=function(e){const r=new Error(e);throw r.code="EBADRPC",r.errno=-72,r},N=(e,r)=>{let i=`Attempting to call a function in a renderer window that has been closed or released.
Function provided here: ${k.get(r)}`;if(e instanceof a.EventEmitter){const n=e.eventNames().filter(s=>e.listeners(s).includes(r));n.length>0&&(i+=`
Remote event names: ${n.join(", ")}`,n.forEach(s=>{e.removeListener(s,r)}))}console.warn(i)},J=(e,r)=>new Proxy(Object,{get(t,i,n){return i==="name"?r:Reflect.get(t,i,n)}}),h=function(e,r,t,i){const n=function(s){switch(s.type){case"nativeimage":return f.deserialize(s.value);case"value":return s.value;case"remote-object":return l.default.get(s.id);case"array":return h(e,r,t,s.value);case"buffer":return Buffer.from(s.value.buffer,s.value.byteOffset,s.value.byteLength);case"promise":return G.resolve({then:n(s.then)});case"object":{const E=s.name!=="Object"?Object.create({constructor:J(Object,s.name)}):{};for(const{name:R,value:p}of s.members)E[R]=n(p);return E}case"function-with-return-value":{const E=n(s.value);return function(){return E}}case"function":{const E=[t,s.id],R=I(E);if(R!==void 0)return R;const p=function(...re){let U=!1;if(!e.isDestroyed())try{U=e.sendToFrame(r,"REMOTE_RENDERER_CALLBACK",t,s.id,b(e,t,re))!==!1}catch(oe){console.warn(`sendToFrame() failed: ${oe}`)}U||N(this,p)};return k.set(p,s.location),Object.defineProperty(p,"length",{value:s.length}),Q(E,e,r,p),p}default:throw new TypeError(`Unknown type: ${s.type}`)}};return i.map(n)},Y=function(e){const r=e.getLastWebPreferences()||{};return r.enableRemoteModule!=null?!!r.enableRemoteModule:!1},T=new WeakMap,x=function(e){return X&&!T.has(e)&&T.set(e,Y(e)),T.get(e)};c.isRemoteModuleEnabled=x;function ee(e){T.set(e,!0)}c.enable=ee;const g=function(e,r){d.ipcMain.on(e,(t,i,...n)=>{let s;if(!c.isRemoteModuleEnabled(t.sender)){t.returnValue={type:"exception",value:b(t.sender,i,new Error('@electron/remote is disabled for this WebContents. Call require("@electron/remote/main").enable(webContents) to enable it.'))};return}try{s=r(t,i,...n)}catch(E){s={type:"exception",value:b(t.sender,i,E)}}s!==void 0&&(t.returnValue=s)})},O=function(e,r,...t){const i={sender:e,returnValue:void 0,defaultPrevented:!1};return d.app.emit(r,i,e,...t),e.emit(r,i,...t),i},y=function(e,r,t){t&&console.warn(`WebContents (${e.id}): ${r}`,t)};let z=!1;function te(){return z}c.isInitialized=te;function ne(){if(z)throw new Error("@electron/remote has already been initialized");z=!0,g("REMOTE_BROWSER_WRONG_CONTEXT_ERROR",function(e,r,t,i){const s=I([t,i]);s!==void 0&&N(e.sender,s)}),g("REMOTE_BROWSER_REQUIRE",function(e,r,t,i){y(e.sender,`remote.require('${t}')`,i);const n=O(e.sender,"remote-require",t);if(n.returnValue===void 0){if(n.defaultPrevented)throw new Error(`Blocked remote.require('${t}')`);if(process.mainModule)n.returnValue=process.mainModule.require(t);else{let s=o;for(;s.parent;)s=s.parent;n.returnValue=s.require(t)}}return b(e.sender,r,n.returnValue)}),g("REMOTE_BROWSER_GET_BUILTIN",function(e,r,t,i){y(e.sender,`remote.getBuiltin('${t}')`,i);const n=O(e.sender,"remote-get-builtin",t);if(n.returnValue===void 0){if(n.defaultPrevented)throw new Error(`Blocked remote.getBuiltin('${t}')`);n.returnValue=w[t]}return b(e.sender,r,n.returnValue)}),g("REMOTE_BROWSER_GET_GLOBAL",function(e,r,t,i){y(e.sender,`remote.getGlobal('${t}')`,i);const n=O(e.sender,"remote-get-global",t);if(n.returnValue===void 0){if(n.defaultPrevented)throw new Error(`Blocked remote.getGlobal('${t}')`);n.returnValue=M[t]}return b(e.sender,r,n.returnValue)}),g("REMOTE_BROWSER_GET_CURRENT_WINDOW",function(e,r,t){y(e.sender,"remote.getCurrentWindow()",t);const i=O(e.sender,"remote-get-current-window");if(i.returnValue===void 0){if(i.defaultPrevented)throw new Error("Blocked remote.getCurrentWindow()");i.returnValue=e.sender.getOwnerBrowserWindow()}return b(e.sender,r,i.returnValue)}),g("REMOTE_BROWSER_GET_CURRENT_WEB_CONTENTS",function(e,r,t){y(e.sender,"remote.getCurrentWebContents()",t);const i=O(e.sender,"remote-get-current-web-contents");if(i.returnValue===void 0){if(i.defaultPrevented)throw new Error("Blocked remote.getCurrentWebContents()");i.returnValue=e.sender}return b(e.sender,r,i.returnValue)}),g("REMOTE_BROWSER_CONSTRUCTOR",function(e,r,t,i){i=h(e.sender,e.frameId,r,i);const n=l.default.get(t);return n==null&&_(`Cannot call constructor on missing remote object ${t}`),b(e.sender,r,new n(...i))}),g("REMOTE_BROWSER_FUNCTION_CALL",function(e,r,t,i){i=h(e.sender,e.frameId,r,i);const n=l.default.get(t);n==null&&_(`Cannot call function on missing remote object ${t}`);try{return b(e.sender,r,n(...i),!0)}catch(s){const E=new Error(`Could not call remote function '${n.name||"anonymous"}'. Check that the function signature is correct. Underlying error: ${s}
`+(s instanceof Error?`Underlying stack: ${s.stack}
`:""));throw E.cause=s,E}}),g("REMOTE_BROWSER_MEMBER_CONSTRUCTOR",function(e,r,t,i,n){n=h(e.sender,e.frameId,r,n);const s=l.default.get(t);return s==null&&_(`Cannot call constructor '${i}' on missing remote object ${t}`),b(e.sender,r,new s[i](...n))}),g("REMOTE_BROWSER_MEMBER_CALL",function(e,r,t,i,n){n=h(e.sender,e.frameId,r,n);const s=l.default.get(t);s==null&&_(`Cannot call method '${i}' on missing remote object ${t}`);try{return b(e.sender,r,s[i](...n),!0)}catch(E){const R=new Error(`Could not call remote method '${i}'. Check that the method signature is correct. Underlying error: ${E}`+(E instanceof Error?`Underlying stack: ${E.stack}
`:""));throw R.cause=E,R}}),g("REMOTE_BROWSER_MEMBER_SET",function(e,r,t,i,n){n=h(e.sender,e.frameId,r,n);const s=l.default.get(t);return s==null&&_(`Cannot set property '${i}' on missing remote object ${t}`),s[i]=n[0],null}),g("REMOTE_BROWSER_MEMBER_GET",function(e,r,t,i){const n=l.default.get(t);return n==null&&_(`Cannot get property '${i}' on missing remote object ${t}`),b(e.sender,r,n[i])}),g("REMOTE_BROWSER_DEREFERENCE",function(e,r,t){l.default.remove(e.sender,r,t)}),g("REMOTE_BROWSER_CONTEXT_RELEASE",(e,r)=>(l.default.clear(e.sender,r),null))}c.initialize=ne})(C,C.exports);var me=C.exports;(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.enable=o.isInitialized=o.initialize=void 0;var c=me;Object.defineProperty(o,"initialize",{enumerable:!0,get:function(){return c.initialize}}),Object.defineProperty(o,"isInitialized",{enumerable:!0,get:function(){return c.isInitialized}}),Object.defineProperty(o,"enable",{enumerable:!0,get:function(){return c.enable}})})(D);var v=D;w.app.commandLine.appendSwitch("disable-site-isolation-trials");v.initialize();w.app.whenReady().then(()=>{const o=new w.BrowserWindow({title:"页匠图片上传工具",width:1440,height:1080,show:!0,webPreferences:{nodeIntegration:!0,contextIsolation:!1}});process.env.VITE_DEV_SERVER_URL?o.loadURL(process.env.VITE_DEV_SERVER_URL):o.loadFile(ue.resolve(__dirname,"../dist/index.html")),v.enable(o.webContents)});
