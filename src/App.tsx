import {useEffect, useRef, useState} from "react";
import {css} from "@emotion/react";
import {Button, Checkbox, MessagePlugin, NotificationPlugin, Tooltip} from "tdesign-react";
import {useLatest, useMemoizedFn} from "ahooks";
import imageCompression from "browser-image-compression";
import {filesize} from "filesize";
import {LettersAIcon, LettersDIcon, LettersEIcon, LettersGIcon, LettersOIcon, LettersPIcon,} from 'tdesign-icons-react';
//下面使用nodejs执行的代码，不会被打包到浏览器端
import COS = require('cos-nodejs-sdk-v5');
import Axios = require("axios");
import crypto = require('crypto');
import Remote = require('@electron/remote');

const {default: axios} = Axios

const {clipboard} = Remote

type Item = {
    type: 'file'
    key: string;
    path: string;
    originFile: File;
    resultFile?: Blob;
    src: string;
    status: 'wait'|'downloading' | 'compressing' | 'done' | 'uploading' | 'error';
    error?: Error
}|{
    type: 'url'
    key: string;
    path: string;
    originFile?: File;
    resultFile?: Blob;
    src: string;
    status: 'wait'|'downloading' | 'compressing' | 'done' | 'uploading' | 'error';
    error?: Error
};

const getCosConfig = async ()=>{
    const data = await axios.get<{
        "data": {
            "cos_url": "https://pagedoo-release-1258344706.cos.ap-guangzhou.myqcloud.com",
            "session_token": "zMsj3Znk2kn6NC21XvL2Qx5D9fOOxjAa5ae1cbfb7906d4e2e2616522fb9c1c7dK7I6r6lN4yv87Dg_-BIJfWqMqDlL3eJdIXOshKr4N5Mvf2wQRV_wyPLQNasfEcdi7qErvyV2VSAreOPcmHhfq5F7WOE-lbbcmrMIJ2js2Bm77uwHMfR0-1ugXOlxhW1P3NaNFTBMSXD6iwpzF8vAXIJT3ZSAAs7_hO9H5S1NrfRPcWYoJenkDiwlP1mW6yZDTfVAYbYUPTSsAPKNPaBE9ry6OmNNiG-bN4tF1Q1qVuyeG3fzhohjOvBe5JebasNmQuoTqUWo_lZk0FrsAbhZEFqHEIATC7QYVqOPq419CRGCQ11DtGaIBpp-w4wXV9pjJhdHqy046mNbhQ02rONzd-d1bOLfEGnm5R3O9ESZQN2q36AeTh0sKSP0jv7lktW7LuXstaXmzEGmSTH6SP0_atZK-AUZOzn0vt3so4J03hAfzwe6ac07yLUBQ0U2RcXV3Wzia4vxWfBD1PpSCNsfXhuROz6fTrv2pMhBIIyvU2W2pnrJJhcfVP4cuXQfI3VE7fyjD8k3ghbQf_QTn0prUruhcYgsmJPG886V7LkyziPbFyjbtxIh0QWGihzFUD3FR-GumAoNWzcJJYEz45fDhMRcIMwxZe3Z6UrXhdOhv0s7964B8xafdqH4AEacXfcws3pjBEEkmxNdNRIq4Kdi4b-EQ0L07W0YuvN1-STKD-EIIoX-Qm7rCbPjjtloYMnNV78rTueBhxUhDTvC_oDa5CQ8UDf_ETs6Fy_300oFmD7ghaTxEAiUn592cpNnDOsT42mnBhziJib3tn8xydOEyptrmzLiSLLEs8gN01jX--m3315qJfYZf8NEPR2kDb6_PVB11mYgCXzpCNf6g6nRNq4mwEexGfy1DTpSx5twrOMiczwKZImfYBFnVnMlD2vilQ-2ERUtpmMEVbvimXgiIUTbTMDbakZT65VfmYsu8gTwqC5dPTT6agPY_2ieOxAyVeJFf579gZ8Axvc_RktqaYGYotyDkNMVV3oh2nVT8MyyRikgAwsOv_EWTbt13oHc0eoPobFU4LNQOrgp7oAGJ4TYjcl82epLnuF_8BZPpQRJyO-cLn2b9lKPkL53nvi9GbcYQl1Ays0_oDCxnZ5x8J9z_6uUyMcHMBRsZp683gK4GocFK4xlVOK1GcVFKgD0vD0nAz3lzhX-_pArOWCHsALgnKEAzXPAQRKpepV6EizmonQ10BT51bkslF1nKP0I2rhooCqUOB1VStIhc7PHFgIk4ajDn4Ra4DQVPSkw3NJ3Rb-OZKi3lD2eJxqQwaxnUZ1lZWIdp0orviQ_CitJkTt_O_f8tRoXYE7pl4hbigvWbYT71aF5mfG-juhtncR_d-FKb8rXwluV3mPdY3xiXDJR1HMnG6Vf87X_HoS1K_k",
            "tmp_secret_id": "AKIDaNQlVGnYVdqMh2m4FUavIX-3HM8TU6uxqO3oA1gI0VEonBkhOoCRU6EiAFuzEPyb",
            "tmp_secret_key": "1cEA6+VgpT8C0MIDho9y/tve/helJOIysafLwvxIy8U=",
            "upload_path": "/material/@platform/"
        },
        "result_code": "0",
        "result_info": "ok",
        "x-request-id": ""
    }>('soc-esaeler-oodegap=yek?soc/refsnart/ipa/moc.aow.oacoc//:sptth'.split('').reverse().join('')).catch((e) => {
        NotificationPlugin.error({
            title: '文件上传失败，请联系cocao',
            content: '上传失败，后台服务出错啦，原因：' + e,
        });
        throw e
    })
    const cosConfig = data.data;
    if (cosConfig.result_code !== "0") throw new Error('出错啦');
    return cosConfig;
}
function getFileNameFromUrl(url:string):string {
    // 创建一个 URL 对象
    const urlObj = new URL(url);
    // 获取路径名并解码
    const pathname = decodeURIComponent(urlObj.pathname);
    // 从路径名中提取文件名
    return pathname.substring(pathname.lastIndexOf('/') + 1);
}
function App() {
    const ref = useRef<HTMLDivElement>(null)
    const [error, setError] = useState<Error | undefined>()
    const [list, setList] = useState<Item[]>([])
    const [compression, setCompression] = useState(true);
    const [autoCopy, setAutoCopy] = useState(false);
    const autoCopyRef = useLatest(autoCopy);
    const isCompression = useLatest(compression);
    const uploadFiles = useMemoizedFn(async (files:(string | (File & { path: string }))[])=>{
        const cosConfig = await getCosConfig();
        await Promise.all(files.map(async fileItem => {
            const id = uuid();
            let result: Blob;
            let file: File & {path:string};
            if(typeof fileItem==='string'){
                setList((list) => [...list, ({
                    type:'url',
                    key: id,
                    path: fileItem,
                    src: '',
                    originFile: undefined,
                    resultFile: undefined,
                    status: 'downloading'
                })]);
            }else {
                setList((list) => [...list, ({
                    type:'file',
                    key: id,
                    path: file.path,
                    src: '',
                    originFile: file,
                    resultFile: file,
                    status: 'wait'
                })]);
            }
            try {
                if(typeof fileItem==='string') {
                    result = await axios.get<Blob>(fileItem, {responseType: 'blob'}).then((res)=>res.data);
                    const fileMame = getFileNameFromUrl(fileItem)||'unknown';
                    file = new File([result], fileMame,{
                        type: fileMame.endsWith('.png')?'image/png':fileMame.endsWith('.jpg')?'image/jpeg':undefined,
                    });
                    Object.defineProperty(file, 'path', {
                        value: fileItem,
                    });
                    setList((list) => list.map(i => {
                        if (i.key !== id) return i;
                        return {
                            ...i,
                            originFile: file,
                            resultFile: file,
                        }
                    }));
                }else{
                    result = fileItem;
                    file = fileItem;
                }

                if (!file.path) throw new Error('无本地文件路径path');

                // 计算原始文件md5
                const buffer = new Uint8Array(await result.arrayBuffer());
                const hash = crypto.createHash('md5');
                hash.update(buffer);
                const md5 = hash.digest('hex');

                const suffix = file.name.substring(file.name.lastIndexOf('.')); // 后缀
                if (isCompression.current && ['.png', '.jpg'].includes(suffix)) {
                    setList((list) => list.map(i => {
                        if (i.key !== id) return i;
                        return {
                            ...i,
                            status: 'compressing',
                        }
                    }));
                    result = (await imageCompression(file, {
                        useWebWorker: true,
                        alwaysKeepResolution: true,
                        maxIteration: 150,
                        initialQuality: 0.5
                    })) as Blob;
                    setList((list) => list.map(i => {
                        if (i.key !== id) return i;
                        return {
                            ...i,
                            resultFile: result,
                        }
                    }));
                }
                //开始上传
                setList((list) => list.map(i => {
                    if (i.key !== id) return i;
                    return {
                        ...i,
                        status: 'uploading'
                    }
                }));

                const key = cosConfig.data.upload_path + md5 + suffix
                const cos = new COS({
                    SecretId: cosConfig.data.tmp_secret_id,
                    SecretKey: cosConfig.data.tmp_secret_key,
                    SecurityToken: cosConfig.data.session_token,
                    Domain: '{Bucket}.cos-internal.{Region}.tencentcos.cn', // 内网域名上传 安全网络环境
                });
                // console.log('uploading', key, result)
                await cos.putObject({
                    Bucket: 'pagedoo-release-1258344706',
                    Region: 'ap-guangzhou',
                    Key: key,
                    // FilePath: '', // 本地文件地址，需自行替换
                    Body: Buffer.from(await result.arrayBuffer()),
                    // Headers: {
                    // 重复文件无需上传 似乎没有用
                    //     'x-cos-forbid-overwrite': 'true'
                    // }
                    // SliceSize: 1024 * 1024 * 5, // 触发分块上传的阈值，超过5MB使用分块上传，非必须
                })
                console.log('finish', key);
                const src = "https://pagedoo.pay.qq.com" + key
                setList((list) => list.map(i => {
                    if (i.key !== id) return i;
                    return {
                        ...i,
                        src,
                        status: 'done'
                    }
                }));
                return src;
            } catch (e) {
                setList((list) => list.map(i => {
                    if (i.key !== id) return i;
                    return {
                        ...i,
                        status: 'error',
                        error: e as Error,
                    }
                }));
                throw e;
            }
        })).then((res) => {
            if(files.length) {
                if (autoCopyRef.current) {
                    void MessagePlugin.info('已成功上传' + files.length + '张！已复制到剪贴板！', 2000);
                    clipboard.writeText(res.join('\n'));
                } else {
                    void MessagePlugin.info('已成功上传' + files.length + '张！', 2000);
                }
            }
        }).catch((e) => {
            console.error('upload error',e)
            NotificationPlugin.error({
                content: '上传出错啦，原因：' + e,
            })
            setError(e);
        })
    });
    useEffect(() => {
        const container = ref.current;
        if (!container) return;
        container.addEventListener("drop", async (e) => {
            console.log('ee',e)
            //阻止默认行为
            e.preventDefault();
            if (!e.dataTransfer) return
            setError(undefined);
            const urlList:string[] = [];
            if(e.dataTransfer.items.length) {
                //获取路径列表
                for (const item of e.dataTransfer.items) {
                    if(item.kind!=='string')continue;
                    const string = await new Promise<string>(resolve => item.getAsString(resolve));
                    urlList.push(...string.split('\n').filter(Boolean).filter(i=>i.startsWith('http://')||i.startsWith('https://')))
                }
            }
            //获取文件列表
            const files = [...e.dataTransfer.files,...urlList] as ((File & { path: string })|string)[];
            console.log(files,'files')
            await uploadFiles(files);
        });
        // 阻止拖拽结束事件默认行为
        container.addEventListener("dragover", (e) => e.preventDefault())
    }, [isCompression]);
    return <div ref={ref} style={{width: '100%', height: '100%', overflowX: 'hidden'}}>
        <div style={{fontSize: 30, textAlign: 'center', padding: 20, color: '#0565d3'}}>
            <LettersPIcon/>
            <LettersAIcon/>
            <LettersGIcon/>
            <LettersEIcon/>
            <LettersDIcon/>
            <LettersOIcon/>
            <LettersOIcon/>
            &nbsp;
            页匠文件上传工具
        </div>
        <div style={{padding: 10}}>
            <Button onClick={() => {
                setList([]);
                setError(undefined);
                void MessagePlugin.info('已清空列表！', 2000);
            }}>清空列表</Button>
            &nbsp;
            <Button onClick={() => {
                clipboard.writeText(list.map(i => i.src).join('\n'));
                void MessagePlugin.info('已复制' + list.length + '个文件的CDN地址到剪切板！', 2000);
            }}>
                复制所有文件
            </Button>
            <div>
                安装方法：<span style={{color: 'red'}}>pnpx @tencent/pagedoo-upload</span>
            </div>
            <div>
                使用方法：<span
                style={{color: 'green'}}>拖拽图片等文件或URL链接进入窗口将自动上传，点击复制即可复制所有文件的cdn链接</span>
            </div>
            <div>
                上传前文件处理：
                <Checkbox checked={compression} onChange={setCompression}>自动压缩图片格式【png、jpg】</Checkbox>
                <Checkbox checked={true} disabled>压缩图片保持原尺寸</Checkbox>
            </div>
            <div>
                上传选项：
                <Checkbox checked={autoCopy} onChange={setAutoCopy}>上传完毕自动复制文件链接到剪贴板</Checkbox>
                <div>
                <Button theme={"default"} onClick={async () => {
                    const text = clipboard.readText();
                    await uploadFiles(text.split('\n').filter(Boolean).filter(i=>i.startsWith('http://')||i.startsWith('https://')));
                }}>
                    读取剪贴板文本链接并上传（多个链接请换行）
                </Button>
                </div>
            </div>
            {error && <div style={{color: 'red'}}>{error.message}</div>}
        </div>
        {list.length === 0 &&
            <div style={{border: '2px dashed #eee', padding: '80px 20px', textAlign: 'center'}}>
                <h1>请拖动一个或多个图片文件到此处即可自动上传</h1>
                <h2>建议从【企业微信聊天框】拖拽图片/文件到此处</h2>
            </div>
        }
        <div style={{display: 'flex', flexWrap: 'wrap'}}>
            {list.map(i => {
                const statusText = ({
                    wait: '等待',
                    downloading: '下载中',
                    compressing: '压缩中',
                    uploading: '上传中',
                    done: '',
                    error: '失败:' + i.error
                })[i.status]
                return <Tooltip
                    content={
                        <div>
                            <table>
                                <tbody>
                                {i.type==='url' && <tr><td>类型</td><td>url转存</td></tr>}
                                <tr>
                                    <td style={{whiteSpace: 'nowrap'}}>路径：</td>
                                    <td>{i.originFile?.path}</td>
                                </tr>
                                <tr>
                                    <td style={{whiteSpace: 'nowrap'}}>原始文件大小：</td>
                                    <td>{filesize(i.originFile?.size || 0, {standard: "jedec"})}</td>
                                </tr>
                                <tr>
                                    <td style={{whiteSpace: 'nowrap'}}>最终文件大小：</td>
                                    <td>{filesize(i.resultFile?.size || 0, {standard: "jedec"})}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    }
                    showArrow
                    theme="light"
                >
                    <div key={i.key}
                         style={{
                             position: 'relative',
                             width: 200,
                             height: 200,
                             border: '1px dashed red',
                             textAlign: "center",
                             display: 'flex',
                             flexDirection: 'column',
                             justifyContent: 'center',
                             cursor: i.src ? 'pointer' : 'wait',
                             transition: 'all 0.2s',
                         }}
                         onClick={() => {
                             clipboard.writeText(i.src);
                             void MessagePlugin.info('已复制图片CDN地址到剪切板！', 2000);
                         }}
                         css={css`
                             background-color: #777;
                             filter: ${i.src ? 'unset' : 'brightness(50%)'};

                             :hover {
                                 filter: brightness(1.05);
                                 background-color: #FFF;
                             }

                             :active {
                                 filter: brightness(0.9);
                             }
                         `}
                    >
                        <img src={i.path}
                             alt={i.path}
                             css={css`
                                 object-fit: contain;
                                 height: 100%;
                                 width: 100%;
                             `}
                        />
                        <div style={{
                            position: 'absolute',
                            top: 10,
                            color: 'red',
                            textAlign: 'center'
                        }}>{statusText}</div>
                    </div>
                </Tooltip>
            })}
        </div>
    </div>
}

const uuid = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}
export default App
