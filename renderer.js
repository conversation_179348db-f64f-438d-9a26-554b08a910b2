// /**
//  * This file is loaded via the <script> tag in the index.html file and will
//  * be executed in the renderer process for that window. No Node.js APIs are
//  * available in this process because `nodeIntegration` is turned off and
//  * `contextIsolation` is turned on. Use the contextBridge API in `preload.js`
//  * to expose Node.js functionality from the main process.
//  */
// const fs = require('fs');
//
// const dragWrapper = document.getElementById("drag_test");
// //添加拖拽事件监听器
// dragWrapper.addEventListener("drop", (e) => {
//     //阻止默认行为
//     e.preventDefault();
//     //获取文件列表
//     const files = e.dataTransfer.files;
//
//     if(files && files.length > 0) {
//         //获取文件路径
//         const path = files[0].path;
//         console.log('path:', path);
//         //读取文件内容
//         const content = fs.readFileSync(path);
//         console.log(content.toString());
//     }
// })
//
// //阻止拖拽结束事件默认行为
// dragWrapper.addEventListener("dragover", (e) => {
//     e.preventDefault();
// })
